<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dashboard – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- Top Navigation -->
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Dashboard</h1>
      <div id="user-display" class="user-display">
        <!-- User info will be populated by auth-utils.js -->
      </div>
    </header>

    <!-- Alert Panel -->
    <div id="alert-panel" class="alert-panel" style="display: none;">
      <!-- Alerts will be populated dynamically -->
    </div>

    <!-- Main Dashboard -->
    <main class="dashboard-main">
      <!-- Current Date Display -->
      <div class="date-display">
        <span class="material-icons">today</span>
        <span id="current-date"></span>
      </div>

      <!-- Dashboard Grid -->
      <div class="dashboard-grid">

        <!-- ANIMALS Section -->
        <section class="dashboard-section clickable-card" onclick="window.location.href='index.html'">
          <h2 class="section-title">
            <span class="material-icons">pets</span>
            Animals
          </h2>

          <div class="stats-cards">
            <div class="stat-card animals-total">
              <div class="stat-icon">
                <span class="material-icons">pets</span>
              </div>
              <div class="stat-content">
                <h3>Total Animals</h3>
                <span class="stat-number" id="total-animals">0</span>
              </div>
            </div>

            <div class="stat-card animals-not-clear">
              <div class="stat-icon">
                <span class="material-icons">warning</span>
              </div>
              <div class="stat-content">
                <h3>Not 'All Clear'</h3>
                <span class="stat-number" id="animals-not-clear">0</span>
              </div>
            </div>

            <div class="stat-card animals-no-log">
              <div class="stat-icon">
                <span class="material-icons">event_busy</span>
              </div>
              <div class="stat-content">
                <h3>No Log Today</h3>
                <span class="stat-number" id="animals-no-log">0</span>
              </div>
            </div>
          </div>
        </section>

        <!-- MEDICAL & WELFARE Section -->
        <section class="dashboard-section clickable-card" onclick="window.location.href='medical-tracker.html'">
          <h2 class="section-title">
            <span class="material-icons">medical_services</span>
            Medical & Welfare
          </h2>

          <div class="stats-cards">
            <div class="stat-card medical-active">
              <div class="stat-icon">
                <span class="material-icons">local_hospital</span>
              </div>
              <div class="stat-content">
                <h3>Active Episodes</h3>
                <span class="stat-number" id="active-episodes">0</span>
              </div>
            </div>

            <div class="stat-card medical-recovery">
              <div class="stat-icon">
                <span class="material-icons">healing</span>
              </div>
              <div class="stat-content">
                <h3>Recovery/Quarantine</h3>
                <span class="stat-number" id="recovery-quarantine">0</span>
              </div>
            </div>
          </div>
        </section>

        <!-- STAFF & USERS Section -->
        <section class="dashboard-section clickable-card" onclick="window.location.href='staff-volunteers.html'">
          <h2 class="section-title">
            <span class="material-icons">people</span>
            Staff & Users
          </h2>

          <div class="stats-cards">
            <div class="stat-card staff-total">
              <div class="stat-icon">
                <span class="material-icons">group</span>
              </div>
              <div class="stat-content">
                <h3>Active Staff/Volunteers</h3>
                <span class="stat-number" id="active-staff">0</span>
              </div>
            </div>

            <div class="stat-card staff-activity">
              <div class="stat-icon">
                <span class="material-icons">today</span>
              </div>
              <div class="stat-content">
                <h3>Activity Today</h3>
                <span class="stat-number" id="staff-activity-today">0</span>
              </div>
            </div>

            <div class="stat-card staff-inactive">
              <div class="stat-icon">
                <span class="material-icons">person_off</span>
              </div>
              <div class="stat-content">
                <h3>Inactive 7+ Days</h3>
                <span class="stat-number" id="inactive-staff">0</span>
              </div>
            </div>
          </div>
        </section>

        <!-- ADOPTION & DONATION Section - Full Width -->
        <section class="dashboard-section dashboard-section-full clickable-card" onclick="window.location.href='donations-adoptions.html'">
          <h2 class="section-title">
            <span class="material-icons">volunteer_activism</span>
            Adoptions & Donations
          </h2>

          <div class="stats-cards">
            <div class="stat-card adoption-expiring">
              <div class="stat-icon">
                <span class="material-icons">schedule</span>
              </div>
              <div class="stat-content">
                <h3>Expiring in 30 Days</h3>
                <span class="stat-number" id="expiring-adoptions">0</span>
              </div>
            </div>
          </div>

          <!-- Chart Container -->
          <div class="chart-container">
            <h3>Monthly Overview</h3>
            <canvas id="monthly-chart"></canvas>
          </div>
        </section>

        <!-- DOCUMENT COMPLIANCE Section -->
        <section class="dashboard-section clickable-card" onclick="window.location.href='documents.html'">
          <h2 class="section-title">
            <span class="material-icons">description</span>
            Document Compliance
          </h2>

          <div class="stats-cards">
            <div class="stat-card documents-total">
              <div class="stat-icon">
                <span class="material-icons">description</span>
              </div>
              <div class="stat-content">
                <h3>Total Documents</h3>
                <span class="stat-number" id="total-documents">0</span>
              </div>
            </div>

            <div class="stat-card documents-policy">
              <div class="stat-icon">
                <span class="material-icons">policy</span>
              </div>
              <div class="stat-content">
                <h3>Policy Documents</h3>
                <span class="stat-number" id="policy-documents">0</span>
              </div>
            </div>

            <div class="stat-card documents-risk">
              <div class="stat-icon">
                <span class="material-icons">warning</span>
              </div>
              <div class="stat-content">
                <h3>Risk Assessments</h3>
                <span class="stat-number" id="risk-documents">0</span>
              </div>
            </div>

            <div class="stat-card documents-emergency">
              <div class="stat-icon">
                <span class="material-icons">emergency</span>
              </div>
              <div class="stat-content">
                <h3>Emergency Procedures</h3>
                <span class="stat-number" id="emergency-documents">0</span>
              </div>
            </div>
          </div>
        </section>

      </div>
    </main>
  </div>

  <!-- Floating Action Button -->
  <div class="fab-container">
    <button class="fab-main" onclick="toggleFabActions()">
      <span class="material-icons">add</span>
    </button>
    <div id="fab-actions" class="fab-actions">
      <button class="fab-action" onclick="window.location.href='index.html'">
        <span class="material-icons">pets</span>
        <span class="label">Add Animal</span>
      </button>
      <button class="fab-action" onclick="window.location.href='reports.html'">
        <span class="material-icons">bar_chart</span>
        <span class="label">Generate Report</span>
      </button>
    </div>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li class="active"><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script src="auth-utils.js"></script>
  <script type="module">
    console.log('🚀 Dashboard starting...');

    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase client
    const { createClient } = await import('https://cdn.skypack.dev/@supabase/supabase-js@2');
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    console.log('✅ Supabase client created');

    // Set current date
    document.getElementById('current-date').textContent = new Date().toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Global variables
    let dashboardData = {};
    let monthlyChart = null;

    // Initialize dashboard
    async function initializeDashboard() {
      try {
        console.log('🔍 Checking authentication...');

        // Check for existing session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
          console.log('❌ No active session, redirecting to login');
          window.location.href = 'login.html';
          return;
        }

        console.log('✅ User authenticated');

        // Load dashboard data
        await loadDashboardData();

        // Update UI
        await updateDashboardUI();

        // Create chart
        createMonthlyChart();

        // Check for alerts
        checkAlerts();

      } catch (error) {
        console.error('Error initializing dashboard:', error);
        showError('Failed to load dashboard data');
      }
    }

    // Load all dashboard data
    async function loadDashboardData() {
      console.log('📊 Loading dashboard data...');

      try {
        // Load animals data
        const { data: animals, error: animalsError } = await supabase
          .from('animals')
          .select('*');

        if (animalsError) throw animalsError;
        dashboardData.animals = animals || [];

        // Load daily logs for today
        const today = new Date().toISOString().split('T')[0];
        const { data: todayLogs, error: logsError } = await supabase
          .from('daily_logs')
          .select('animal_id')
          .gte('created_on', today + 'T00:00:00')
          .lt('created_on', today + 'T23:59:59');

        if (logsError) throw logsError;
        dashboardData.todayLogs = todayLogs || [];

        // Load medical episodes
        const { data: episodes, error: episodesError } = await supabase
          .from('medical_episodes')
          .select('*');

        if (episodesError) throw episodesError;
        dashboardData.episodes = episodes || [];

        // Load staff/volunteers
        const { data: staff, error: staffError } = await supabase
          .from('staff_volunteers')
          .select('*');

        if (staffError) throw staffError;
        dashboardData.staff = staff || [];

        // Load adoptions/donations
        const { data: adoptions, error: adoptionsError } = await supabase
          .from('adoptions_donations')
          .select('*');

        if (adoptionsError) throw adoptionsError;
        dashboardData.adoptions = adoptions || [];

        // Load documents
        const { data: documents, error: documentsError } = await supabase
          .from('documents')
          .select('*');

        if (documentsError) throw documentsError;
        dashboardData.documents = documents || [];

        console.log('✅ Dashboard data loaded');

      } catch (error) {
        console.error('Error loading dashboard data:', error);
        throw error;
      }
    }

    // Update dashboard UI with loaded data
    async function updateDashboardUI() {
      console.log('🎨 Updating dashboard UI...');

      // Animals stats
      const totalAnimals = dashboardData.animals.length;
      const animalsNotClear = dashboardData.animals.filter(a => a.status !== 'All Clear').length;
      const loggedAnimalIds = new Set(dashboardData.todayLogs.map(log => log.animal_id));
      const animalsNoLog = dashboardData.animals.filter(a => !loggedAnimalIds.has(a.id)).length;

      document.getElementById('total-animals').textContent = totalAnimals;
      document.getElementById('animals-not-clear').textContent = animalsNotClear;
      document.getElementById('animals-no-log').textContent = animalsNoLog;

      // Medical stats
      const activeEpisodes = dashboardData.episodes.filter(e => e.status === 'active').length;
      const recoveryQuarantine = dashboardData.animals.filter(a =>
        a.status === 'Recovery' || a.status === 'Quarantined'
      ).length;

      document.getElementById('active-episodes').textContent = activeEpisodes;
      document.getElementById('recovery-quarantine').textContent = recoveryQuarantine;

      // Staff stats
      const activeStaff = dashboardData.staff.filter(s => s.status === 'Active').length;
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const inactiveStaff = dashboardData.staff.filter(s =>
        !s.last_login_at || new Date(s.last_login_at) < sevenDaysAgo
      ).length;

      document.getElementById('active-staff').textContent = activeStaff;
      document.getElementById('staff-activity-today').textContent = '0'; // Placeholder
      document.getElementById('inactive-staff').textContent = inactiveStaff;

      // Adoptions stats
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      const expiringAdoptions = dashboardData.adoptions.filter(a =>
        a.end_date && new Date(a.end_date) <= thirtyDaysFromNow
      ).length;

      document.getElementById('expiring-adoptions').textContent = expiringAdoptions;

      // Documents stats
      await loadDocumentStats();
    }

    // Load document statistics from Supabase
    async function loadDocumentStats() {
      try {
        console.log('📄 Loading document statistics...');

        // Get all documents from the database
        const { data: documents, error } = await supabase
          .from('documents')
          .select('category');

        if (error) {
          console.error('Error loading documents:', error);
          // Set default values on error
          document.getElementById('total-documents').textContent = '0';
          document.getElementById('policy-documents').textContent = '0';
          document.getElementById('risk-documents').textContent = '0';
          document.getElementById('emergency-documents').textContent = '0';
          return;
        }

        const docs = documents || [];

        // Count documents by category
        const totalDocs = docs.length;
        const policyDocs = docs.filter(doc => doc.category === 'policy').length;
        const riskDocs = docs.filter(doc => doc.category === 'risk-assessment').length;
        const emergencyDocs = docs.filter(doc => doc.category === 'emergency').length;

        // Update the UI
        document.getElementById('total-documents').textContent = totalDocs;
        document.getElementById('policy-documents').textContent = policyDocs;
        document.getElementById('risk-documents').textContent = riskDocs;
        document.getElementById('emergency-documents').textContent = emergencyDocs;

        console.log(`✅ Document stats loaded: ${totalDocs} total (${policyDocs} policy, ${riskDocs} risk, ${emergencyDocs} emergency)`);

      } catch (error) {
        console.error('Error in loadDocumentStats:', error);
        // Set default values on error
        document.getElementById('total-documents').textContent = '0';
        document.getElementById('policy-documents').textContent = '0';
        document.getElementById('risk-documents').textContent = '0';
        document.getElementById('emergency-documents').textContent = '0';
      }
    }

    // Create monthly chart
    function createMonthlyChart() {
      const ctx = document.getElementById('monthly-chart').getContext('2d');

      // Sample data - replace with real data processing
      const monthlyData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Adoptions',
          data: [12, 19, 3, 5, 2, 3],
          backgroundColor: 'rgba(76, 175, 80, 0.6)',
          borderColor: 'rgba(76, 175, 80, 1)',
          borderWidth: 2
        }, {
          label: 'Donations',
          data: [7, 11, 5, 8, 3, 7],
          backgroundColor: 'rgba(33, 150, 243, 0.6)',
          borderColor: 'rgba(33, 150, 243, 1)',
          borderWidth: 2
        }]
      };

      monthlyChart = new Chart(ctx, {
        type: 'bar',
        data: monthlyData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
            },
            title: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    // Check for alerts
    function checkAlerts() {
      const alerts = [];

      // Animals with no log today
      const loggedAnimalIds = new Set(dashboardData.todayLogs.map(log => log.animal_id));
      const animalsNoLog = dashboardData.animals.filter(a => !loggedAnimalIds.has(a.id));

      if (animalsNoLog.length > 0) {
        alerts.push({
          type: 'warning',
          icon: 'event_busy',
          message: `${animalsNoLog.length} animals have no log entries today`,
          action: () => window.location.href = 'index.html'
        });
      }

      // Inactive staff
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const inactiveStaff = dashboardData.staff.filter(s =>
        !s.last_login_at || new Date(s.last_login_at) < sevenDaysAgo
      );

      if (inactiveStaff.length > 0) {
        alerts.push({
          type: 'info',
          icon: 'person_off',
          message: `${inactiveStaff.length} staff members inactive for 7+ days`,
          action: () => window.location.href = 'staff-volunteers.html'
        });
      }

      displayAlerts(alerts);
    }

    // Display alerts
    function displayAlerts(alerts) {
      const alertPanel = document.getElementById('alert-panel');

      if (alerts.length === 0) {
        alertPanel.style.display = 'none';
        return;
      }

      alertPanel.innerHTML = alerts.map(alert => `
        <div class="alert alert-${alert.type}">
          <span class="material-icons">${alert.icon}</span>
          <span class="alert-message">${alert.message}</span>
          <button class="alert-action" onclick="(${alert.action})()">View</button>
          <button class="alert-dismiss" onclick="this.parentElement.remove()">
            <span class="material-icons">close</span>
          </button>
        </div>
      `).join('');

      alertPanel.style.display = 'block';
    }

    // Show error message
    function showError(message) {
      console.error(message);
      // Could implement toast notification here
    }

    // FAB functionality
    function toggleFabActions() {
      const fabActions = document.getElementById('fab-actions');
      fabActions.classList.toggle('active');
    }

    // Menu functionality
    function toggleMenu() {
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (sideMenu.classList.contains('open')) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
      }
    }

    // Make functions globally available
    window.toggleFabActions = toggleFabActions;
    window.toggleMenu = toggleMenu;

    // Initialize dashboard
    initializeDashboard();
  </script>
</body>
</html>
