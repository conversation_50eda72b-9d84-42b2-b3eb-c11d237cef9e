<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Login – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <!-- Login Form -->
    <main class="login-main">
      <div class="login-container">
        <div class="login-header">
          <h1>Cornish Birds of Prey</h1>
          <p>Staff & Volunteer Access</p>
        </div>

        <!-- Login Form -->
        <div id="login-form-container" class="auth-form-container">
          <form id="login-form" class="auth-form">
            <h2>Sign In</h2>
            
            <div class="form-group">
              <label for="login-email">Email Address</label>
              <input type="email" id="login-email" name="email" required placeholder="<EMAIL>">
            </div>

            <div class="form-group">
              <label for="login-password">Password</label>
              <div class="password-input-container">
                <input type="password" id="login-password" name="password" required placeholder="Enter your password" autocomplete="current-password">
                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('login-password')">
                  <span class="material-icons">visibility</span>
                </button>
              </div>
            </div>

            <div class="form-options">
              <label class="checkbox-label">
                <input type="checkbox" id="remember-me" name="remember">
                <span>Remember me</span>
                <span class="checkmark"></span>
              </label>
              <a href="#" onclick="showForgotPassword()" class="forgot-password-link">Forgot Password?</a>
            </div>

            <button type="submit" class="btn-primary auth-btn">
              <span class="material-icons">login</span>
              Sign In
            </button>

            <div id="login-feedback" class="feedback"></div>
          </form>

          <!-- Biometric Login Option (shown after first successful login) -->
          <div id="biometric-login" class="biometric-section" style="display: none;">
            <div class="divider">
              <span>or</span>
            </div>
            <button type="button" id="biometric-btn" class="btn-secondary auth-btn">
              <span class="material-icons">fingerprint</span>
              Use Biometric Login
            </button>
          </div>
        </div>

        <!-- Forgot Password Form -->
        <div id="forgot-password-container" class="auth-form-container" style="display: none;">
          <form id="forgot-password-form" class="auth-form">
            <h2>Reset Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password.</p>
            
            <div class="form-group">
              <label for="reset-email">Email Address</label>
              <input type="email" id="reset-email" name="email" required placeholder="<EMAIL>">
            </div>

            <button type="submit" class="btn-primary auth-btn">
              <span class="material-icons">email</span>
              Send Reset Link
            </button>

            <button type="button" class="btn-secondary auth-btn" onclick="showLogin()">
              <span class="material-icons">arrow_back</span>
              Back to Login
            </button>

            <div id="reset-feedback" class="feedback"></div>
          </form>
        </div>

        <!-- 2FA Verification Form -->
        <div id="mfa-container" class="auth-form-container" style="display: none;">
          <form id="mfa-form" class="auth-form">
            <h2>Two-Factor Authentication</h2>
            <p>Enter the verification code sent to your phone.</p>
            
            <div class="form-group">
              <label for="mfa-code">Verification Code</label>
              <input type="text" id="mfa-code" name="code" required placeholder="123456" maxlength="6" pattern="[0-9]{6}">
            </div>

            <button type="submit" class="btn-primary auth-btn">
              <span class="material-icons">verified</span>
              Verify
            </button>

            <button type="button" class="btn-secondary auth-btn" onclick="resendMFACode()">
              <span class="material-icons">refresh</span>
              Resend Code
            </button>

            <div id="mfa-feedback" class="feedback"></div>
          </form>
        </div>


      </div>


    </main>
  </div>

  <script type="module">
    // Supabase configuration
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase client
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Global variables
    let currentUser = null;
    let sessionTimeout = null;
    let mfaSession = null;

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      checkExistingSession();
      setupEventListeners();
      checkBiometricSupport();
    });

    // Check for existing session
    async function checkExistingSession() {
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        currentUser = session.user;
        await loadUserProfile();

        goToDashboard();
      }
    }

    // Setup event listeners
    function setupEventListeners() {
      // Login form
      document.getElementById('login-form').addEventListener('submit', handleLogin);

      // Forgot password form
      document.getElementById('forgot-password-form').addEventListener('submit', handleForgotPassword);

      // MFA form
      document.getElementById('mfa-form').addEventListener('submit', handleMFAVerification);



      // Biometric login
      const biometricBtn = document.getElementById('biometric-btn');
      if (biometricBtn) {
        biometricBtn.addEventListener('click', handleBiometricLogin);
      }

      // Auto-logout setup
      setupAutoLogout();
    }

    // Handle login form submission
    async function handleLogin(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const email = formData.get('email');
      const password = formData.get('password');
      const remember = formData.get('remember');

      try {
        showFeedback('login-feedback', 'Signing in...', 'info');

        const { data, error } = await supabase.auth.signInWithPassword({
          email: email,
          password: password
        });

        if (error) {
          throw error;
        }

        currentUser = data.user;
        await loadUserProfile();

        // Check if MFA is enabled
        if (currentUser.mfa_enabled) {
          showMFAForm();
          return;
        }

        // Update last login
        await updateLastLogin();

        // Set session persistence
        if (remember) {
          localStorage.setItem('rememberUser', 'true');
        }

        showFeedback('login-feedback', 'Login successful! Redirecting...', 'success');

        setTimeout(() => {
          goToDashboard();
        }, 1500);

      } catch (error) {
        console.error('Login error:', error);
        showFeedback('login-feedback', error.message || 'Login failed. Please try again.', 'error');
      }
    }

    // Load user profile from staff_volunteers table
    async function loadUserProfile() {
      try {
        const { data, error } = await supabase
          .from('staff_volunteers')
          .select('*')
          .eq('user_id', currentUser.id)
          .single();

        if (error) {
          throw error;
        }

        currentUser.profile = data;
        currentUser.role = data.user_role;
        currentUser.mfa_enabled = data.two_factor_enabled || false;

      } catch (error) {
        console.error('Error loading user profile:', error);
        showFeedback('login-feedback', 'Error loading user profile. Please contact an administrator.', 'error');
      }
    }

    // Handle forgot password
    async function handleForgotPassword(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const email = formData.get('email');

      try {
        showFeedback('reset-feedback', 'Sending reset link...', 'info');

        const { error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/login.html?reset=true`
        });

        if (error) {
          throw error;
        }

        showFeedback('reset-feedback', 'Password reset link sent! Check your email.', 'success');

        setTimeout(() => {
          showLogin();
        }, 3000);

      } catch (error) {
        console.error('Password reset error:', error);
        showFeedback('reset-feedback', error.message || 'Failed to send reset link.', 'error');
      }
    }

    // Handle MFA verification
    async function handleMFAVerification(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const code = formData.get('code');

      try {
        showFeedback('mfa-feedback', 'Verifying code...', 'info');

        // This would integrate with your MFA provider (e.g., Twilio)
        // For now, we'll simulate the verification
        const isValid = await verifyMFACode(code);

        if (!isValid) {
          throw new Error('Invalid verification code');
        }

        await updateLastLogin();
        showFeedback('mfa-feedback', 'Verification successful! Redirecting...', 'success');

        setTimeout(() => {
          goToDashboard();
        }, 1500);

      } catch (error) {
        console.error('MFA verification error:', error);
        showFeedback('mfa-feedback', error.message || 'Verification failed.', 'error');
      }
    }

    // Verify MFA code (placeholder - integrate with your SMS provider)
    async function verifyMFACode(code) {
      // This is a placeholder - you'd integrate with Twilio or similar
      // For demo purposes, accept '123456' as valid
      return code === '123456';
    }

    // Resend MFA code
    async function resendMFACode() {
      try {
        showFeedback('mfa-feedback', 'Sending new code...', 'info');

        // This would trigger a new SMS via your provider
        // For demo purposes, just show success
        showFeedback('mfa-feedback', 'New verification code sent!', 'success');

      } catch (error) {
        showFeedback('mfa-feedback', 'Failed to send new code.', 'error');
      }
    }



    // Update last login timestamp
    async function updateLastLogin() {
      try {
        await supabase
          .from('staff_volunteers')
          .update({
            last_login_at: new Date().toISOString(),
            last_login: new Date().toISOString() // backwards compatibility
          })
          .eq('user_id', currentUser.id);
      } catch (error) {
        console.error('Error updating last login:', error);
      }
    }

    // Auto-logout functionality
    function setupAutoLogout() {
      let timeoutId;
      const TIMEOUT_MINUTES = 10;
      const TIMEOUT_MS = TIMEOUT_MINUTES * 60 * 1000;

      function resetTimeout() {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          logout(true); // auto logout
        }, TIMEOUT_MS);
      }

      // Reset timeout on user activity
      ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, resetTimeout, true);
      });

      resetTimeout(); // Initial setup
    }

    // Logout function
    async function logout(isAutoLogout = false) {
      try {
        await supabase.auth.signOut();
        localStorage.removeItem('rememberUser');

        if (isAutoLogout) {
          alert('You have been automatically logged out due to inactivity.');
        }

        // Redirect to login
        window.location.reload();

      } catch (error) {
        console.error('Logout error:', error);
      }
    }

    // UI Helper Functions
    function showLogin() {
      document.getElementById('login-form-container').style.display = 'block';
      document.getElementById('forgot-password-container').style.display = 'none';
      document.getElementById('mfa-container').style.display = 'none';
      document.getElementById('create-user-container').style.display = 'none';
    }

    function showForgotPassword() {
      document.getElementById('login-form-container').style.display = 'none';
      document.getElementById('forgot-password-container').style.display = 'block';
    }

    function showMFAForm() {
      document.getElementById('login-form-container').style.display = 'none';
      document.getElementById('mfa-container').style.display = 'block';
    }





    function goToDashboard() {
      window.location.href = 'dashboard.html';
    }

    function togglePasswordVisibility(inputId) {
      const input = document.getElementById(inputId);
      const icon = input.nextElementSibling.querySelector('.material-icons');

      if (input.type === 'password') {
        input.type = 'text';
        icon.textContent = 'visibility_off';
      } else {
        input.type = 'password';
        icon.textContent = 'visibility';
      }
    }

    function showFeedback(elementId, message, type) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="feedback ${type}">${message}</div>`;
    }

    // Biometric authentication support
    function checkBiometricSupport() {
      if (window.PublicKeyCredential) {
        // Check if user has biometric enabled and show option
        const savedBiometric = localStorage.getItem('biometricEnabled');
        if (savedBiometric === 'true') {
          document.getElementById('biometric-login').style.display = 'block';
        }
      }
    }

    async function handleBiometricLogin() {
      try {
        // This is a placeholder for WebAuthn implementation
        showFeedback('login-feedback', 'Biometric authentication not yet implemented.', 'info');
      } catch (error) {
        showFeedback('login-feedback', 'Biometric authentication failed.', 'error');
      }
    }

    // Make functions globally available
    window.showLogin = showLogin;
    window.showForgotPassword = showForgotPassword;
    window.goToDashboard = goToDashboard;
    window.logout = logout;
    window.togglePasswordVisibility = togglePasswordVisibility;
    window.resendMFACode = resendMFACode;

  </script>
</body>
</html>
