/* ===== GENERAL APP STYLES ===== */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 1rem;
  background: #4285a6;
  background: linear-gradient(47deg, rgb(127, 186, 215) 0%, rgb(162, 210, 163) 59%, rgb(253, 246, 181) 100%);
  background-attachment: fixed;
  min-height: 100vh;
  color: #333;
  position: relative;
}

/* Add subtle grey shading to both sides for curved background effect */
body::before,
body::after {
  content: '';
  position: fixed;
  top: 0;
  bottom: 0;
  width: 15%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.03), transparent);
  pointer-events: none;
  z-index: -1;
}

body::before {
  left: 0;
}

body::after {
  right: 0;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.03), transparent);
}

/* Main container with frosted glass effect - Mobile First */
.app-container {
  width: 95%;
  max-width: 1400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  min-height: calc(100vh - 2rem);
}

/* Desktop adjustments */
@media (min-width: 768px) {
  body {
    padding: 2rem;
  }

  .app-container {
    width: 90%;
    padding: 2rem;
    border-radius: 30px;
    min-height: calc(100vh - 4rem);
  }
}

img {
  max-width: 100%;
  height: 100%;
  display: block;
  object-position: top;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

h1 {
  font-size: 2rem;
  color: #333;
}

h2 {
  font-size: 1.5rem;
  color: #444;
}

h3 {
  font-size: 1.25rem;
  color: #555;
}

p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid rgba(66, 133, 166, 0.5);
  outline-offset: 2px;
}

/* ===== HEADER AND NAVIGATION STYLES ===== */

/* Header - Mobile First */
.top-bar {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  color: #2c3e50;
  padding: 1rem;
  border-radius: 15px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  position: relative;
}

.top-bar h1 {
  font-size: 1.2rem;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 120px);
  margin: 0;
}

/* Desktop header adjustments */
@media (min-width: 768px) {
  .top-bar {
    padding: 1.5rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
  }

  .top-bar h1 {
    font-size: 1.8rem;
    max-width: none;
  }
}

.menu-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.7);
}

/* Header Styling */
.top-bar .title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
}

.back-link {
  color: #2c3e50;
  text-decoration: none;
  font-size: 1.5rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
}

.back-link:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* User Display in Top Bar */
.user-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.8rem;
  color: #666;
  font-weight: 400;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.logout-btn .material-icons {
  font-size: 1.2rem;
  color: #2c3e50;
}

/* Mobile adjustments for user display */
@media (max-width: 768px) {
  .user-display {
    gap: 0.5rem;
  }

  .user-name {
    font-size: 0.8rem;
  }

  .user-role {
    font-size: 0.7rem;
  }

  .logout-btn .material-icons {
    font-size: 1rem;
  }
}

/* Side Menu Enhancements */
.side-menu {
  position: fixed;
  top: 0;
  left: -250px;
  width: 250px;
  height: 100vh;
  background: rgba(44, 62, 80, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: left 0.3s ease;
  z-index: 1000;
  padding: 0;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
}

.side-menu.open {
  left: 0;
}

.menu-header {
  padding: 2rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-header h2,
.menu-header h3 {
  margin: 0;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-menu-btn,
.close-menu {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-menu-btn:hover,
.close-menu:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-menu .material-icons {
  font-size: 1.2rem;
}

.side-menu ul,
.menu-items {
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.side-menu li {
  margin: 0;
}

.menu-items li {
  margin: 0;
}

.side-menu a,
.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.side-menu a .material-icons,
.menu-item .material-icons {
  font-size: 1.2rem;
  min-width: 1.2rem;
}

.side-menu a:hover,
.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: rgba(87, 199, 89, 0.8);
}

.side-menu a.active,
.side-menu li.active a,
.menu-item.active {
  background: rgba(66, 133, 166, 0.2);
  color: white;
  border-left-color: rgba(66, 133, 166, 0.8);
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ===== UNIVERSAL FORM AND BUTTON STYLES ===== */

/* Form styling to match frosted glass theme - Apply to all forms */
.form-content,
.form-group,
form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-content label,
.form-group label,
form label {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  color: #333;
  gap: 0.5rem;
}

/* Override existing input styles with frosted glass theme */
.form-content input,
.form-content select,
.form-content textarea,
.form-group input,
.form-group select,
.form-group textarea,
form input,
form select,
form textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  font-size: 1rem;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.form-content input:focus,
.form-content select:focus,
.form-content textarea:focus,
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus,
form input:focus,
form select:focus,
form textarea:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(66, 133, 166, 0.1);
}

.form-content textarea,
.form-group textarea,
form textarea {
  min-height: 100px;
  resize: vertical;
}

.form-content select,
.form-group select,
form select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Universal button styling for forms */
.cancel-btn,
form button[type="cancel"],
.submit-btn,
form button[type="submit"],
.modal-content button[type="submit"],
.btn-primary,
.btn-secondary,
button[type="submit"],
.fab,
.floating-button,
.print-button,
#toggle-logs {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 44px;
}

.submit-btn,
form button[type="submit"],
.modal-content button[type="submit"],
.btn-primary,
button[type="submit"] {
  background: rgba(66, 133, 166, 0.8);
  color: white;
  border-color: rgba(66, 133, 166, 0.9);
}

.cancel-btn,
form button[type="cancel"],
.btn-secondary {
  background: rgba(255, 255, 255, 0.6);
  color: #2c3e50;
  border-color: rgba(255, 255, 255, 0.4);
}

.submit-btn:hover,
form button[type="submit"]:hover,
.modal-content button[type="submit"]:hover,
.btn-primary:hover,
button[type="submit"]:hover,
.cancel-btn:hover,
form button[type="cancel"]:hover,
.btn-secondary:hover,
.fab:hover,
.floating-button:hover,
.print-button:hover,
#toggle-logs:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.submit-btn:hover,
form button[type="submit"]:hover,
.modal-content button[type="submit"]:hover,
.btn-primary:hover,
button[type="submit"]:hover {
  background: rgba(66, 133, 166, 0.7);
  color: white;
  border-color: rgba(66, 133, 166, 0.8);
}

.cancel-btn:hover,
form button[type="cancel"]:hover,
.btn-secondary:hover,
.fab:hover,
.floating-button:hover,
.print-button:hover,
#toggle-logs:hover {
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
}

/* ===== MODAL AND OVERLAY STYLES ===== */

/* Modal Styles - Mobile First */
.modal,
#log-modal,
#edit-animal-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  align-items: flex-start;
  justify-content: center;
  padding: 1rem;
  overflow-y: auto;
}

.modal-content {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 2rem;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  position: relative;
  margin-top: 2rem;
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

/* Desktop modal adjustments */
@media (min-width: 768px) {
  .modal,
  #log-modal,
  #edit-animal-modal {
    align-items: center;
    padding: 2rem;
  }

  .modal-content {
    width: 90%;
    margin-top: 0;
    max-height: 90vh;
  }
}

/* Modal Enhancements - Mobile First */
.modal-content.large {
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  margin-top: 1rem;
}

/* Desktop large modal adjustments */
@media (min-width: 768px) {
  .modal-content.large {
    width: 90%;
    margin-top: 0;
  }
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  color: #666;
}

.close-modal:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

/* ===== FLOATING ACTION BUTTONS ===== */

/* Floating Button */
.fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 999;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Floating Action Button Section */
.floating-actions {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 998;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Floating Action Menu Styles */
.fab-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.fab-main {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(66, 133, 166, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  font-size: 1.5rem;
}

.fab-main:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
  background: rgba(66, 133, 166, 1);
}

.fab-actions {
  position: absolute;
  bottom: 80px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.fab-container.active .fab-actions {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-action {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 25px;
  padding: 0.75rem 1rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  white-space: nowrap;
}

.fab-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.fab-action .material-icons {
  font-size: 20px;
}

/* ===== PRINT STYLES ===== */

/* Print Overrides */
@media print {
  .print-button,
  .floating-button,
  .floating-actions,
  #log-feedback,
  .modal,
  #log-modal,
  #edit-animal-modal {
    display: none !important;
  }
}

/* Print Styles - Hide interactive elements when printing */
@media print {
  .fab-container,
  .fab-main,
  .fab-actions,
  .fab-action,
  .floating-actions,
  .floating-button,
  .print-button,
  .menu-btn,
  .back-link,
  .logout-btn,
  .side-menu,
  .menu-overlay,
  .modal,
  .modal-content,
  .close-modal,
  .fab,
  .add-log-btn,
  .edit-animal-btn,
  .delete-animal-btn,
  .logs-section .form-group,
  .logs-section form,
  .logs-section button {
    display: none !important;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Error Message Styling */
.error-message {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  color: #d32f2f;
  font-weight: 500;
  margin: 2rem 0;
}

/* No Results Message Styling */
.no-results {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  color: #666;
  font-weight: 500;
  margin: 2rem 0;
  font-size: 1.1rem;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(66, 133, 166, 0.2);
  border-top: 4px solid rgba(66, 133, 166, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast Notification */
.toast {
  position: fixed;
  top: 20%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 1rem 1.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #2c3e50;
  max-width: 90%;
  text-align: center;
}

.toast.show {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

.toast.success {
  border-left: 4px solid #4CAF50;
}

.toast.error {
  border-left: 4px solid #f44336;
}

.toast.success::before {
  content: "✅ ";
  margin-right: 0.5rem;
}

.toast.error::before {
  content: "❌ ";
  margin-right: 0.5rem;
}

/* User Attribution Styles */
.user-attribution {
  background: rgba(66, 133, 166, 0.1);
  border: 1px solid rgba(66, 133, 166, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin: 1rem 0;
}

.user-attribution label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
}

.user-attribution .readonly-field {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(66, 133, 166, 0.3);
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #333;
  cursor: not-allowed;
}

.user-attribution .readonly-field:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
}

/* User attribution display in lists/cards */
.record-attribution {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.record-attribution .created-by::before {
  content: "Created by: ";
  font-weight: 600;
}

.record-attribution .updated-by::before {
  content: "Last updated by: ";
  font-weight: 600;
}

/* ===== LOGIN PAGE STYLES ===== */

/* Authentication & Login Styles */
.login-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 2rem);
  padding: 2rem 0;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.login-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Auth Form Container */
.auth-form-container {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.auth-form h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.auth-form p {
  text-align: center;
  color: #666;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

/* Form Help Text */
.form-help {
  display: block;
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.25rem;
}

.password-requirements {
  display: block;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Password Input Container */
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #2c3e50;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #2c3e50;
}

.checkbox-container input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.forgot-password-link {
  color: rgba(66, 133, 166, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: rgba(66, 133, 166, 1);
  text-decoration: underline;
}

/* Remember Me Checkbox Specific Styling */
.remember-me-label {
  display: flex;
  flex-direction: row !important;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #2c3e50;
  position: relative;
  font-weight: normal !important;
}

.remember-me-label input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
}

.remember-me-checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(66, 133, 166, 0.5);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.remember-me-label input[type="checkbox"]:checked ~ .remember-me-checkmark {
  background: rgba(66, 133, 166, 0.8);
  border-color: rgba(66, 133, 166, 0.9);
}

.remember-me-label input[type="checkbox"]:checked ~ .remember-me-checkmark::after {
  content: '✓';
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Auth Buttons */
.auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  margin: 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.auth-btn .material-icons {
  font-size: 1.2rem;
}

/* Biometric Section */
.biometric-section {
  margin-top: 1rem;
}

.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
}

.divider span {
  background: rgba(255, 255, 255, 0.6);
  padding: 0 1rem;
  color: #666;
  font-size: 0.9rem;
}

/* Password Requirements */
.password-requirements {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
  margin-top: 0.5rem;
  display: block;
}

/* Admin Actions */
.admin-actions {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  margin-top: 1rem;
}

.admin-actions h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.admin-actions button {
  margin: 0.5rem;
  min-width: 200px;
}

/* Mobile Responsive Styles for Authentication */
@media (max-width: 768px) {
  .login-main {
    padding: 1rem 0;
    min-height: calc(100vh - 1rem);
  }

  .login-container {
    max-width: 100%;
    padding: 0 1rem;
  }

  .auth-form-container {
    padding: 1.5rem;
    border-radius: 15px;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .login-header p {
    font-size: 1rem;
  }

  .auth-form h2 {
    font-size: 1.3rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .checkbox-container {
    justify-content: center;
  }

  .forgot-password-link {
    text-align: center;
  }

  .admin-actions {
    padding: 1.5rem;
  }

  .admin-actions button {
    min-width: 100%;
    margin: 0.25rem 0;
  }

  .password-requirements {
    font-size: 0.75rem;
  }
}

/* ===== DASHBOARD PAGE STYLES ===== */

/* Dashboard */
.dashboard {
  padding: 0;
}

/* Dashboard Styles - Mobile First */
.dashboard-main {
  padding: 1rem 0;
}

.date-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #2c3e50;
  font-weight: 600;
}

.date-display .material-icons {
  color: #4285a6;
}

/* Alert Panel */
.alert-panel {
  margin-bottom: 1rem;
}

.alert {
  background: rgba(255, 193, 7, 0.9);
  color: #856404;
  border: 1px solid rgba(255, 193, 7, 0.5);
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(15px);
}

.alert.warning {
  background: rgba(255, 152, 0, 0.9);
  color: #e65100;
  border-color: rgba(255, 152, 0, 0.5);
}

.alert.error {
  background: rgba(244, 67, 54, 0.9);
  color: #c62828;
  border-color: rgba(244, 67, 54, 0.5);
}

.alert-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.alert-actions {
  display: flex;
  gap: 0.5rem;
}

.alert-action, .alert-dismiss {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.alert-action:hover, .alert-dismiss:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
}

/* Dashboard Grid - Mobile First */
.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.dashboard-section {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
}

.section-title .material-icons {
  color: #4285a6;
}

/* Stats Cards - Mobile First (Single Column) */
.stats-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.7);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon .material-icons {
  font-size: 1.8rem;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

/* Card Color Variations */
.animals-total .stat-icon { background: rgba(76, 175, 80, 0.2); }
.animals-total .stat-icon .material-icons { color: #4CAF50; }

.animals-not-clear .stat-icon { background: rgba(255, 152, 0, 0.2); }
.animals-not-clear .stat-icon .material-icons { color: #FF9800; }

.medical-episodes .stat-icon { background: rgba(244, 67, 54, 0.2); }
.medical-episodes .stat-icon .material-icons { color: #f44336; }

.daily-logs .stat-icon { background: rgba(33, 150, 243, 0.2); }
.daily-logs .stat-icon .material-icons { color: #2196F3; }

.staff-active .stat-icon { background: rgba(76, 175, 80, 0.2); }
.staff-active .stat-icon .material-icons { color: #4CAF50; }

.staff-inactive .stat-icon { background: rgba(121, 85, 72, 0.2); }
.staff-inactive .stat-icon .material-icons { color: #795548; }

.documents-review .stat-icon { background: rgba(255, 193, 7, 0.2); }
.documents-review .stat-icon .material-icons { color: #FFC107; }

/* Chart Container */
.chart-container {
  margin-top: 1rem;
}

.chart-container h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
}

.chart-container canvas {
  max-height: 300px;
}

/* Clickable Dashboard Cards */
.clickable-card {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.clickable-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.clickable-card:active {
  transform: translateY(0);
}

/* Dashboard Tablet Styles */
@media (min-width: 768px) {
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .dashboard-section-full {
    grid-column: span 2;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .stat-card {
    padding: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .chart-container canvas {
    max-height: 350px;
  }
}

/* Dashboard Desktop Styles */
@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  .dashboard-section-full {
    grid-column: span 3;
  }

  .dashboard-section-half {
    grid-column: span 2;
  }

  .stats-cards {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

.search-bar {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  font-size: 1rem;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.search-bar:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(66, 133, 166, 0.1);
}

.search-bar::placeholder {
  color: rgba(44, 62, 80, 0.6);
}

/* Controls Container */
.controls-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Filter Dropdowns */
.filter-dropdowns {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.filter-dropdown {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  font-size: 1rem;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.filter-dropdown:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(66, 133, 166, 0.2);
}

/* Animal Cards - Mobile First with 2 Column Grid */
.animal-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 0;
}

.animal-card {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.animal-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
}

.animal-card .image-container {
  width: 100%;
  height: 120px;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.75rem;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.animal-card .image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
  transition: transform 0.3s ease;
}

.animal-card:hover .image-container img {
  transform: scale(1.05);
}

.animal-card .name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  text-align: center;
}

.animal-card .status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
  text-align: center;
  color: white;
}

/* Status Badge Colors Based on Status */
.animal-card .status-badge {
  background: #4CAF50; /* Default green for "All Clear" */
}

/* All Clear - Green */
.animal-card .status-badge[data-status="all clear"],
.animal-card .status-badge[data-status="healthy"],
.animal-card .status-badge[data-status="released"],
.animal-card .status-badge[data-status="recovered"] {
  background: #4CAF50;
}

/* Under Observation - Light Green */
.animal-card .status-badge[data-status="under observation"] {
  background: #8BC34A;
}

/* Unwell - Yellow */
.animal-card .status-badge[data-status="unwell"] {
  background: #CDDC39;
  color: #333;
}

/* Vet Booked - Orange */
.animal-card .status-badge[data-status="vet booked"] {
  background: #FF9800;
}

/* In Treatment - Orange-Red */
.animal-card .status-badge[data-status="in treatment"] {
  background: #FF5722;
}

/* Recovery - Blue */
.animal-card .status-badge[data-status="recovery"] {
  background: #2196F3;
}

/* Ongoing Condition - Purple */
.animal-card .status-badge[data-status="ongoing condition"] {
  background: #9C27B0;
}

/* Palliative - Dark Purple */
.animal-card .status-badge[data-status="palliative"] {
  background: #673AB7;
}

/* Quarantined - Red */
.animal-card .status-badge[data-status="quarantined"] {
  background: #df3427;
}

/* Transferred - Pink */
.animal-card .status-badge[data-status="transferred"] {
  background: #ff3679;
}

/* Deceased - Black */
.animal-card .status-badge[data-status="deceased"] {
  background: #424242;
}

.animal-card .details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.animal-card .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  line-height: 1.3;
}

.animal-card .detail-label {
  color: #666;
  font-weight: 500;
}

.animal-card .detail-value {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: right;
}

/* Medium tablet adjustments (768px - 1020px) */
@media (min-width: 768px) and (max-width: 1020px) {
  .controls-container {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-dropdowns {
    flex-direction: column;
    gap: 0.75rem;
  }

  .animal-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .animal-card .image-container {
    height: 140px;
  }

  .animal-card .name {
    font-size: 1.2rem;
  }

  .animal-card .detail-value {
    font-size: 1rem;
    text-align: right;
  }
}

/* Large desktop adjustments */
@media (min-width: 1021px) {
  .controls-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .filter-dropdowns {
    flex-direction: row;
    gap: 1rem;
    min-width: 400px;
  }

  .animal-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .animal-card {
    padding: 1.25rem;
  }

  .animal-card .image-container {
    height: 160px;
    margin-bottom: 1rem;
  }

  .animal-card .name {
    font-size: 1.3rem;
    margin-bottom: 0.75rem;
  }

  .animal-card .status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    margin-bottom: 1rem;
  }

  .animal-card .details {
    gap: 0.75rem;
  }

  .animal-card .detail-row {
    font-size: 0.9rem;
  }

  .animal-card .detail-value {
    font-size: 1rem;
    text-align: right;
  }
}

/* CRITICAL MOBILE OVERRIDE - Must be at end of file */
@media (max-width: 767px) {
  /* Force mobile layout regardless of other styles */
  .animal-cards {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  .animal-card {
    padding: 0.75rem !important;
    border-radius: 12px !important;
  }

  .animal-card .image-container {
    height: 100px !important;
    margin-bottom: 0.5rem !important;
    border-radius: 8px !important;
  }

  .animal-card .name {
    font-size: 1rem !important;
    margin-bottom: 0.4rem !important;
  }

  .animal-card .status-badge {
    font-size: 0.7rem !important;
    padding: 0.2rem 0.6rem !important;
    margin-bottom: 0.5rem !important;
  }

  .animal-card .details {
    gap: 0.25rem !important;
  }

  .animal-card .detail-row {
    font-size: 0.8rem !important;
  }

  .animal-card .detail-label {
    font-size: 0.75rem !important;
  }

  .animal-card .detail-value {
    font-size: 0.8rem !important;
  }
}

/* Extra small mobile devices (max-width: 400px) */
@media (max-width: 400px) {
  .animal-cards {
    gap: 0.4rem !important;
  }

  .animal-card {
    padding: 0.5rem !important;
  }

  .animal-card .image-container {
    height: 80px !important;
    margin-bottom: 0.4rem !important;
  }

  .animal-card .name {
    font-size: 0.9rem !important;
    margin-bottom: 0.3rem !important;
  }

  .animal-card .status-badge {
    font-size: 0.65rem !important;
    padding: 0.15rem 0.5rem !important;
    margin-bottom: 0.4rem !important;
  }

  .animal-card .detail-row {
    font-size: 0.75rem !important;
  }

  .animal-card .detail-label {
    font-size: 0.7rem !important;
  }

  .animal-card .detail-value {
    font-size: 0.75rem !important;
  }
}

/* ===== ANIMAL DETAIL PAGE STYLES ===== */

/* Animal Detail Page - Mobile First */
.animal-detail {
  width: 95%;
  max-width: 650px;
  margin: 1rem auto;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.animal-header {
  text-align: center;
  margin-bottom: 2rem;
}

.animal-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.animal-photo {
  width: 200px;
  height: 200px;
  border-radius: 15px;
  margin: 0 auto 1rem;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.animal-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Mobile-first info layout - labels above values */
.info-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  line-height: 1.4;
}

.info-label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 1rem;
  color: #333;
  white-space: pre-line;
}

/* Desktop adjustments */
@media (min-width: 768px) {
  .animal-detail {
    width: 90%;
    margin: 2rem auto;
    padding: 2.5rem;
  }

  .animal-header h1 {
    font-size: 2.2rem;
  }

  .animal-photo {
    width: 250px;
    height: 250px;
  }

  .info-row {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
  }

  .info-label {
    flex: 0 0 30%;
    font-size: 1rem;
  }

  .info-value {
    flex: 1;
    font-size: 1.1rem;
    text-align: right;
  }
}

.qr-container {
  margin: 2rem auto 1rem;
}

/* ===== MEDICAL TRACKER PAGE STYLES ===== */

/* Medical Tracker Styles */
.medical-tracker {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  padding-top: 2rem;
}

.medical-header {
  text-align: center;
  margin-bottom: 2rem;
}

.medical-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.medical-header .animal-name {
  color: #666;
  font-size: 1.2rem;
  font-weight: 500;
}

/* Episodes Section */
.episodes-section {
  margin-bottom: 3rem;
}

.episodes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.episodes-header h2 {
  color: #2c3e50;
  font-size: 1.5rem;
}

.episode-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.episode-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.7);
}

.episode-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.episode-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.episode-status {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.episode-status.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.episode-status.resolved {
  background: rgba(158, 158, 158, 0.2);
  color: #666;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

/* ===== STAFF & VOLUNTEERS PAGE STYLES ===== */

/* Staff & Volunteers Module Styles */
.staff-volunteers-main {
  padding: 0;
}

.search-filter-section {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Staff & Volunteers Grid - Mobile First */
.staff-volunteers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 0;
}

/* Person Cards */
.person-card {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.person-card:hover {
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* ===== DOCUMENTS PAGE STYLES ===== */

/* Document Management Styles */
.documents-main {
  padding: 2rem 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* Upload Section */
.upload-section {
  margin-bottom: 3rem;
}

.upload-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.upload-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.7);
}

/* Documents Section */
.documents-section {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Documents Grid View - Mobile First */
.documents-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.6);
}

/* ===== DONATIONS & ADOPTIONS PAGE STYLES ===== */

/* Donations & Adoptions Module Styles */
.donations-adoptions-main {
  padding: 0;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.7);
}

/* Table Section */
.table-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.data-table th {
  background: rgba(66, 133, 166, 0.1);
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-table tr:hover {
  background: rgba(66, 133, 166, 0.05);
}

/* ===== REPORTS PAGE STYLES ===== */

/* Report Type Buttons */
.report-type-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.report-type-btn {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  text-decoration: none;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.report-type-btn:hover,
.report-type-btn.active {
  background: rgba(66, 133, 166, 0.8);
  color: white;
  border-color: rgba(66, 133, 166, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.report-type-btn .material-icons {
  font-size: 1.1rem;
}

/* Report Builder Sections */
.report-builder {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* ===== EMERGENCY CONTACTS PAGE STYLES ===== */

/* Emergency Contacts Main */
.emergency-contacts-main {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Priority Sections */
.priority-section {
  margin-bottom: 2rem;
}

.priority-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
}

/* Contacts Grid */
.contacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

/* Contact Cards */
.contact-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.contact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.7);
}
