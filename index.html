<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Cornish Birds of Prey – Dashboard</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />

</head>
<body>
  <div class="app-container">
    <!-- Top Navigation -->
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Cornish Birds of Prey</h1>
      <div id="user-display" class="user-display">
        <!-- User info will be populated by auth-utils.js -->
      </div>
    </header>

    <!-- Main Dashboard -->
    <main class="dashboard">
      <!-- Filter and Search Controls -->
      <div class="controls-container">
        <div class="filter-dropdowns">
          <select id="group-filter" class="filter-dropdown">
            <option value="all">All Groups</option>
          </select>
          <select id="status-filter" class="filter-dropdown">
            <option value="all">All Status</option>
          </select>
        </div>
        <input class="search-bar" type="text" placeholder="Search animals..." />
      </div>

      <section class="animal-cards">
        <!-- Cards dynamically inserted here -->
      </section>
    </main>
  </div>

  <!-- Floating Button -->
  <button class="fab" onclick="document.getElementById('new_animal-modal').style.display='flex'">＋ Add a New Animal</button>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li class="active"><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script src="auth-utils.js"></script>
  <script type="module">
    console.log('🚀 Dashboard starting...');

    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase client
    const { createClient } = await import('https://cdn.skypack.dev/@supabase/supabase-js@2');
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    console.log('✅ Supabase client created');

    // Global variables for search functionality
    let allAnimals = [];
    let filteredAnimals = [];
    let currentGroupFilter = 'all';
    let currentStatusFilter = 'all';
    let currentSearchTerm = '';

    async function fetchAnimals() {
      try {
        // Use Supabase client instead of direct fetch to get proper auth headers
        const { data: animals, error } = await supabase
          .from('animals')
          .select('*');

        if (error) {
          throw error;
        }

        // Store animals globally for search functionality
        allAnimals = animals || [];
        filteredAnimals = [...allAnimals];

        // Render the animals
        renderAnimals(filteredAnimals);

      } catch (error) {
        console.error('Error fetching animals:', error);
        const container = document.querySelector('.animal-cards');
        container.innerHTML = '<div class="error-message">Error loading animals. Please try again.</div>';
      }
    }

    // Function to render animals
    function renderAnimals(animals) {
      const container = document.querySelector('.animal-cards');
      container.innerHTML = ''; // Clear existing cards

      if (animals.length === 0) {
        container.innerHTML = '<div class="no-results">No animals found matching your search.</div>';
        return;
      }

      animals.forEach(animal => {
        const card = document.createElement('article');
        card.className = 'animal-card';
        card.dataset.id = animal.id; // ✅ Used for linking to animal.html

        // Create status badge with data attribute for CSS styling
        const status = animal.status || 'Unknown';
        const statusDataAttr = status.toLowerCase();

        card.innerHTML = `
          <div class="photo-container">
            <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name || 'Animal'} Photo" />
          </div>
          <div class="info">
            <div class="header">
              <h2 class="name">${animal.name || 'Unnamed'}</h2>
              <span class="status-badge" data-status="${statusDataAttr}">${status.toUpperCase()}</span>
            </div>
            <div class="details">
              <div class="detail-row">
                <span class="detail-label">Species:</span>
                <span class="detail-value">${animal.species || 'Unknown'}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Age:</span>
                <span class="detail-value">${animal.age || 'Unknown'}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Group:</span>
                <span class="detail-value">${animal.Group || animal.group || 'Unknown'}</span>
              </div>
            </div>
          </div>
        `;

        container.appendChild(card);
      });
    }

    // Simple authentication check and fetch animals
    async function initializePage() {
      try {
        console.log('🔍 Checking authentication...');

        // Check for existing session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session error:', sessionError);
          window.location.href = 'login.html';
          return;
        }

        if (!session) {
          console.log('❌ No active session, redirecting to login');
          window.location.href = 'login.html';
          return;
        }

        console.log('✅ User authenticated:', session.user.email);

        // Update user display
        const userDisplay = document.getElementById('user-display');
        if (userDisplay) {
          // Extract name from email (part before @) or use full name if available
          const displayName = session.user.user_metadata?.full_name ||
                             session.user.user_metadata?.name ||
                             session.user.email.split('@')[0];

          userDisplay.innerHTML = `
            <div class="user-info">
              <span class="user-name">${displayName}</span>
            </div>
            <button class="logout-btn" onclick="logout()">
              <span class="material-icons">logout</span>
            </button>
          `;
        }

        // Fetch animals
        await fetchAnimals();

        // Setup filter dropdowns and search functionality after animals are loaded
        populateFilterDropdowns();
        setupSearchBar();

      } catch (error) {
        console.error('Error initializing page:', error);
        window.location.href = 'login.html';
      }
    }

    // Logout function
    async function logout() {
      try {
        await supabase.auth.signOut();
        window.location.href = 'login.html';
      } catch (error) {
        console.error('Logout error:', error);
        window.location.href = 'login.html';
      }
    }

    // Make logout globally available
    window.logout = logout;

    initializePage();

    // Combined filtering function
    function applyFilters() {
      console.log('🔍 Applying filters - Search:', currentSearchTerm, 'Group:', currentGroupFilter, 'Status:', currentStatusFilter);
      console.log('📊 Total animals:', allAnimals.length);

      let filtered = [...allAnimals];

      // Apply group filter first
      if (currentGroupFilter !== 'all') {
        filtered = filtered.filter(animal => {
          const animalGroup = animal.Group || animal.group || '';
          return animalGroup.toLowerCase() === currentGroupFilter.toLowerCase();
        });
        console.log('🏷️ After group filter:', filtered.length);
      }

      // Apply status filter
      if (currentStatusFilter !== 'all') {
        filtered = filtered.filter(animal => {
          const animalStatus = animal.status || '';
          return animalStatus.toLowerCase() === currentStatusFilter.toLowerCase();
        });
        console.log('📊 After status filter:', filtered.length);
      }

      // Apply search filter
      if (currentSearchTerm.trim()) {
        const term = currentSearchTerm.toLowerCase().trim();
        filtered = filtered.filter(animal => {
          // Add null checks to prevent errors
          const animalName = animal.name || '';
          const animalSpecies = animal.species || '';

          const nameMatch = animalName.toLowerCase().includes(term);
          const speciesMatch = animalSpecies.toLowerCase().includes(term);
          return nameMatch || speciesMatch;
        });
        console.log('🔎 After search filter:', filtered.length);
      }

      filteredAnimals = filtered;
      console.log('🎯 Final filtered results:', filteredAnimals.length);

      // Re-render the filtered animals
      renderAnimals(filteredAnimals);
    }

    // Search functionality
    function searchAnimals(searchTerm) {
      currentSearchTerm = searchTerm;
      applyFilters();
    }

    // Group filter functionality
    function filterByGroup(group) {
      console.log('🏷️ Filtering by group:', group);
      currentGroupFilter = group;
      applyFilters();
    }

    // Status filter functionality
    function filterByStatus(status) {
      console.log('📊 Filtering by status:', status);
      currentStatusFilter = status;
      applyFilters();
    }

    // Populate filter dropdowns based on unique values
    function populateFilterDropdowns() {
      // Populate Group dropdown
      const groupFilter = document.getElementById('group-filter');
      if (groupFilter) {
        // Get unique groups from all animals
        const uniqueGroups = [...new Set(allAnimals.map(animal => animal.Group || animal.group).filter(group => group))];
        console.log('🏷️ Unique groups found:', uniqueGroups);

        // Clear existing options (except "All Groups")
        groupFilter.innerHTML = '<option value="all">All Groups</option>';

        // Add options for each unique group
        uniqueGroups.sort().forEach(group => {
          const option = document.createElement('option');
          option.value = group.toLowerCase();
          option.textContent = group;
          groupFilter.appendChild(option);
        });

        // Add event listener
        groupFilter.addEventListener('change', (e) => filterByGroup(e.target.value));
        console.log('✅ Group dropdown populated');
      }

      // Populate Status dropdown
      const statusFilter = document.getElementById('status-filter');
      if (statusFilter) {
        // Get unique statuses from all animals
        const uniqueStatuses = [...new Set(allAnimals.map(animal => animal.status).filter(status => status))];
        console.log('📊 Unique statuses found:', uniqueStatuses);

        // Clear existing options (except "All Status")
        statusFilter.innerHTML = '<option value="all">All Status</option>';

        // Add options for each unique status
        uniqueStatuses.sort().forEach(status => {
          const option = document.createElement('option');
          option.value = status.toLowerCase();
          option.textContent = status;
          statusFilter.appendChild(option);
        });

        // Add event listener
        statusFilter.addEventListener('change', (e) => filterByStatus(e.target.value));
        console.log('✅ Status dropdown populated');
      }
    }

    // Setup search bar functionality
    function setupSearchBar() {
      const searchBar = document.querySelector('.search-bar');
      console.log('🔍 Setting up search bar:', searchBar);

      if (searchBar) {
        // Add input event for instant search
        searchBar.addEventListener('input', function(e) {
          console.log('📝 Input event triggered:', e.target.value);
          searchAnimals(e.target.value);
        });

        // Also handle keyup for better responsiveness
        searchBar.addEventListener('keyup', function(e) {
          console.log('⌨️ Keyup event triggered:', e.target.value);
          searchAnimals(e.target.value);
        });

        console.log('✅ Search bar event listeners attached');
      } else {
        console.error('❌ Search bar not found!');
      }
    }

    // ✅ Handle click to navigate to animal.html
    document.addEventListener('click', function (e) {
      const card = e.target.closest('.animal-card');
      if (card && card.dataset.id) {
        const animalId = card.dataset.id;
        window.location.href = `animal.html?id=${animalId}`;
      }
    });

    // Handle form submission
    const animalForm = document.getElementById('animal-form');
    if (animalForm) {
      animalForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const animalData = {
          name: formData.get('name'),
          species: formData.get('species'),
          Group: formData.get('Group'),
          status: formData.get('status'),
          notes: formData.get('notes') || '',
          created_at: new Date().toISOString()
        };

        // Handle photo
        const photoFile = formData.get('photo');
        const photoUrl = formData.get('photo_url');

        if (photoFile && photoFile.size > 0) {
          // Upload file to Supabase storage
          const fileName = `${Date.now()}_${photoFile.name}`;
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('animal-photos')
            .upload(fileName, photoFile);

          if (uploadError) {
            console.error('Photo upload error:', uploadError);
            alert('Error uploading photo. Animal will be saved without photo.');
          } else {
            const { data: { publicUrl } } = supabase.storage
              .from('animal-photos')
              .getPublicUrl(fileName);
            animalData.photo_url = publicUrl;
          }
        } else if (photoUrl) {
          animalData.photo_url = photoUrl;
        }

        // Add user attribution
        const animalDataWithAttribution = window.authUtils.addUserAttribution(animalData, false);

        // Save to database
        const { data, error } = await supabase
          .from('animals')
          .insert([animalDataWithAttribution])
          .select();

        if (error) {
          console.error('Error saving animal:', error);
          alert('Error saving animal. Please try again.');
        } else {
          alert('Animal saved successfully!');
          closeAddModal();
        }
      });
    }
  </script>

<!-- Add Animal Modal -->
<div id="new_animal-modal" class="new_animal-input">
  <div class="modal-content">
    <span class="close-modal" onclick="closeAddModal()">&times;</span>
    <h2 style="margin-top: 0; margin-bottom: 1.5rem; color: #333;">Add New Animal</h2>

    <form id="animal-form" class="form-content">
      <label>
        Name:
        <input type="text" name="name" required />
      </label>

      <label>
        Species:
        <input type="text" name="species" required />
      </label>

      <label>
        Group:
        <select name="Group" required>
          <option value="">--Select Group--</option>
          <option value="Livestock">Livestock</option>
          <option value="Other (Miscellaneous)">Other (Miscellaneous)</option>
          <option value="Owls">Owls</option>
          <option value="Parrots">Parrots</option>
          <option value="Raptor">Raptor</option>
          <option value="Waterfoul">Waterfoul</option>
        </select>
      </label>

      <label>
        Status:
        <select name="status" required>
          <option value="">--Select--</option>
          <option value="All Clear">All Clear</option>
          <option value="Under Observation">Under Observation</option>
          <option value="Unwell">Unwell</option>
          <option value="Vet Booked">Vet Booked</option>
          <option value="In Treatment">In Treatment</option>
          <option value="Recovery">Recovery</option>
          <option value="Ongoing Condition">Ongoing Condition</option>
          <option value="Palliative">Palliative</option>
          <option value="Quarantined">Quarantined</option>
          <option value="Transferred">Transferred</option>
          <option value="Deceased">Deceased</option>
        </select>
      </label>

      <label>
        Photo:
        <div style="margin-bottom: 0.5rem;">
          <button type="button" id="upload-tab" onclick="switchPhotoMethod('upload')" class="photo-tab active-tab">Upload File</button>
          <button type="button" id="url-tab" onclick="switchPhotoMethod('url')" class="photo-tab">Enter URL</button>
        </div>

        <div id="upload-method" style="display: block;">
          <input type="file" name="photo" accept="image/*" id="photo-upload" />
          <small style="color: #666; font-size: 0.9rem;">Choose an image file (JPG, PNG, etc.)</small>
        </div>

        <div id="url-method" style="display: none;">
          <input type="url" name="photo_url" id="photo-url" placeholder="https://example.com/image.jpg" />
          <small style="color: #666; font-size: 0.9rem;">Enter a direct link to an image</small>
        </div>
      </label>

      <div id="photo-preview" style="display: none; margin-top: 0.5rem;">
        <img id="preview-image" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #ddd;" />
        <button type="button" onclick="removePhoto()" class="remove-photo-btn">Remove Photo</button>
      </div>

      <label>
        Notes:
        <textarea name="notes" rows="4"></textarea>
      </label>

      <!-- User Attribution will be added here by JavaScript -->

      <button type="submit" class="submit-btn">Save Animal</button>
    </form>
  </div>
</div>
</div>

<script>
function openAddModal() {
  // Add user attribution to the form
  window.authUtils.addUserAttributionToForm('animal-form');

  document.getElementById('new_animal-modal').style.display = 'flex';
}

function closeAddModal() {
  document.getElementById('new_animal-modal').style.display = 'none';
  setTimeout(() => {
    location.reload();
  }, 300);
}

// Menu functionality
function toggleMenu() {
  console.log('toggleMenu called');
  const sideMenu = document.getElementById('side-menu');
  const overlay = document.getElementById('menu-overlay');

  if (!sideMenu || !overlay) {
    console.error('Menu elements not found:', { sideMenu, overlay });
    return;
  }

  const isOpen = sideMenu.classList.contains('open');
  console.log('Menu is currently:', isOpen ? 'open' : 'closed');

  if (isOpen) {
    sideMenu.classList.remove('open');
    overlay.classList.remove('active');
    console.log('Menu closed');
  } else {
    sideMenu.classList.add('open');
    overlay.classList.add('active');
    console.log('Menu opened');
  }
}

// Photo handling functions
function switchPhotoMethod(method) {
  const uploadMethod = document.getElementById('upload-method');
  const urlMethod = document.getElementById('url-method');
  const uploadTab = document.getElementById('upload-tab');
  const urlTab = document.getElementById('url-tab');

  if (method === 'upload') {
    uploadMethod.style.display = 'block';
    urlMethod.style.display = 'none';
    uploadTab.classList.add('active-tab');
    urlTab.classList.remove('active-tab');
    document.getElementById('photo-url').value = '';
  } else {
    uploadMethod.style.display = 'none';
    urlMethod.style.display = 'block';
    urlTab.classList.add('active-tab');
    uploadTab.classList.remove('active-tab');
    document.getElementById('photo-upload').value = '';
  }
  hidePhotoPreview();
}

function showPhotoPreview(src) {
  const preview = document.getElementById('photo-preview');
  const img = document.getElementById('preview-image');
  img.src = src;
  preview.style.display = 'block';
}

function hidePhotoPreview() {
  document.getElementById('photo-preview').style.display = 'none';
}

function removePhoto() {
  document.getElementById('photo-upload').value = '';
  document.getElementById('photo-url').value = '';
  hidePhotoPreview();
}

// Photo preview handlers
document.addEventListener('DOMContentLoaded', function() {
  const photoUpload = document.getElementById('photo-upload');
  const photoUrl = document.getElementById('photo-url');

  if (photoUpload) {
    photoUpload.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          showPhotoPreview(e.target.result);
        };
        reader.readAsDataURL(file);
      } else {
        hidePhotoPreview();
      }
    });
  }

  if (photoUrl) {
    photoUrl.addEventListener('input', function(e) {
      const url = e.target.value.trim();
      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
        showPhotoPreview(url);
      } else {
        hidePhotoPreview();
      }
    });
  }
});
</script>
</body>
</html>